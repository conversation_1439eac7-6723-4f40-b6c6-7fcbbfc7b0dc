const escapeRegex = (str) => str.replace(/[-[\]/{}()*+?.\\^$|]/g, '\\$&');

const STOP_WORDS = [
  'Customer Care No',
  'Customer Care Number',
  'Contact No',
  'Contact Numbers',
  'Toll Free No',
  'Toll Free Number',
  'Toll Free Numbers',
  'Helpline No',
  'Helpline Number',
  'Helpline Numbers',
  'Call Center No',
  'Call Center Number',
  'Call Center Numbers',
  'Customer Care Service',
  'Customer Support No',
  'Customer Support Number',
  'Customer Support Numbers',
  'Customer Helpline No',
  'Customer Helpline Number',
  'Customer Helpline Numbers',
  'Support No',
  'Support Number',
  'Support Numbers',
  'Helpdesk No',
  'Helpdesk Number',
  'Helpdesk Numbers',
  'Enquiry No',
  'Enquiry Number',
  'Enquiry Numbers',
  'Service Center',
  'Service Centers',
  'Customer Care',
  'Customer Support',
  'Customer Service',
  'Customer Helpline',
  'Call Center',
  'Call Centre',
  'Contact',
  'Helpline',
  'Helpdesk',
  'Support',
  'Enquiry',
  'Toll Free',
  'Toll-Free',
  'Phone No.',
  'Phone No',
  'Phone Number',
  'Phone Numbers',
  'Tel No.',
  'Tel No',
  'Telephone No.',
  'Telephone No',
  'Telephone Number',
  'Care No.',
  'Care No',
  'Care Number',
  'Care Numbers',
];

const STOP_WORDS_REGEX = new RegExp(
  `\\b(?:${STOP_WORDS.map(escapeRegex).join('|')})\\b`,
  'i'
);

const PHONE_FIELDS = {
  field_toll_free_numbers2: 'TOLL_FREE',
  field_all_india_numbers: 'ALL_INDIA',
  field_international_no: 'INTERNATIONAL',
};

const PLATFORM_PATTERNS = {
  WEBSITE: /^(https?:\/\/)?([a-z0-9-]+\.)+[a-z]{2,}(\/.*)?$/i,
  FACEBOOK: /facebook\.com/i,
  INSTAGRAM: /instagram\.com/i,
  LINKEDIN: /linkedin\.com/i,
  YOUTUBE: /youtube\.com|youtu\.be/i,
  X: /(?:twitter\.com|x\.com)/i,
  APP_LINK: /play\.google\.com|apps\.apple\.com/i,
};

const REGEXES = {
  companyName: new RegExp(
    `^([\\w&@.\\-\\s()]+?)(?=\\s*(?:${STOP_WORDS.map(escapeRegex).join(
      '|'
    )})\\b|\\s+\\d{3,})`,
    'i'
  ),
  phoneSplit: /^([+()0-9.\-\s]+)[\s-]*[([]?(.*?)[)\]]?$/,
  // phoneSplit: /^\s*([\*\+]?(?:\(?\+?\d{1,4}\)?[\s.-]*)?(?:\(0\)[\s.-]*)?(?:[\d\-.\s]{3,}\d)?)\s*[\[(（]?(.*?)[\])）]?\s*$/i,
  // phoneSplit: /^\s*([\*\+]?(?:\(?\+?\d{1,4}\)?[\s.-]*)?(?:\(0\)[\s.-]*)?(?:[\d\-.\s]{3,}\d)?)\s*[\[(（]?(.*?)[\])）]?\s*$/i,
  url: /https?:\/\/[^\s"')]+/g,
};

const extractUrls = (obj) => {
  const urls = new Set();
  const walk = (o) => {
    if (Array.isArray(o)) o.forEach(walk);
    else if (typeof o === 'object' && o !== null)
      Object.values(o).forEach(walk);
    else if (typeof o === 'string') {
      const found = o.match(REGEXES.url);
      if (found) found.forEach((u) => urls.add(u));
    }
  };
  walk(obj);
  return [...urls];
};

const cleanNumber = (number) => number.replace(/[([{][^)\]}]*$/, '').trim();

const extractCompanyName = (title) => {
  if (!title) return null;

  const regexMatch = title.match(REGEXES.companyName);
  if (regexMatch && regexMatch[1].trim()) {
    return regexMatch[1].trim();
  }

  const withoutNumbers = title.replace(/\s+[\d\-().]{3,}.*$/, '').trim();
  let splitResult = withoutNumbers.split(STOP_WORDS_REGEX)[0]?.trim();

  let cleanedResult = splitResult || withoutNumbers;

  for (const stopWord of STOP_WORDS) {
    const concatenatedRegex = new RegExp(
      `(.+?)${escapeRegex(stopWord)}.*$`,
      'i'
    );
    const match = cleanedResult.match(concatenatedRegex);
    if (match && match[1].trim()) {
      cleanedResult = match[1].trim();
      break;
    }
  }

  return cleanedResult;
};

let totalContact = 0;

function processJsonDataHelper(jsonData) {
  const finalOutput = [];

  jsonData.forEach((entry) => {
    const title = entry.title?.trim() || null;
    const parentCompany = entry.field_company?.[0]?.value?.trim() || null;
    const companyId = entry.nid;
    const companyName = extractCompanyName(title);
    // const companyName = title;

    const company = {
      id: companyId,
      company_name: companyName,
      parent_company: parentCompany,
      company_email: entry.field_email?.[0]?.value || null,
      company_logo_url: entry.field_company_logo?.[0]?.filepath
        ? '/' + entry.field_company_logo[0].filepath
        : null,
      company_country: entry.field_country?.[0]?.value || null,
      company_address: entry.field_address?.[0]?.value || null,
      company_website: entry.field_website?.[0]?.url || null,
      last_updated_at: entry.changed ? Number(entry.changed) : null,
    };

    // const contact = [];
    // for (const [field, type] of Object.entries(PHONE_FIELDS)) {
    //   if (entry[field]) {
    //     entry[field].forEach((item) => {
    //       if (item?.value) {
    //         const trimmed = item.value.trim();
    //         const match = trimmed.match(REGEXES.phoneSplit);
    //         console.log(`Processing field: ${match}`);

    //         if (trimmed && match && match[1]) {
    //           const number = cleanNumber(match[1].trim().replace(/\s+/g, ' '));
    //           const description = match[2]?.trim() || null;
    //           contact.push({
    //             company_id: companyId,
    //             contact_type: type,
    //             number: trimmed,
    //             contact_description: null,
    //             is_whatsapp: /whatsapp/i.test(trimmed),
    //           });
    //           totalContact++;
    //         }
    //         // if (!match || !match[1]) {
    //         //   console.warn(
    //         //     `Invalid phone format in field ${field} for company ID ${companyId}: ${trimmed}`
    //         //   );
    //         // }
    //       }
    //     });
    //   }
    // }

    const contact = [];
    for (const [field, type] of Object.entries(PHONE_FIELDS)) {
      if (entry[field]) {
        entry[field].forEach((item) => {
          if (item?.value) {
            const trimmed = item.value.trim();
            const match = trimmed.match(REGEXES.phoneSplit);
            // console.log(`Processing field: ${match}`);

            if (trimmed && match && match[1]) {
              const number = cleanNumber(match[1].trim().replace(/\s+/g, ' '));
              const description = match[2]?.trim() || null;
              contact.push({
                company_id: companyId,
                contact_type: type,
                number,
                contact_description: description,
                is_whatsapp: /whatsapp/i.test(number + description),
              });
              totalContact++;
            }
            if (!match || !match[1]) {
              console.warn(
                `Invalid phone format in field ${field} for company ID ${companyId}: ${trimmed}`
              );
            }
          }
        });
      }
    }

    const taxonomy = entry.taxonomy || {};
    const category = Object.values(taxonomy)
      .filter((t) => t.vid === '5')
      .map((t) => ({
        id: t.tid,
        company_id: companyId,
        category_name: t.name,
      }));

    if (category.length === 0) {
      category.push({
        id: 1,
        company_id: companyId,
        category_name: 'Other',
      });
    }

    const taxonomyList = Object.values(taxonomy).map((t) => ({
      id: t.tid,
      name: t.name,
      description: '',
      weight: t.weight || null,
      weight_unused: t.weight_unused || null,
      vid: t.vid,
    }));

    const allUrls = extractUrls(entry);
    const seen = new Set();
    const url = [];

    for (const u of allUrls) {
      for (const [type, regex] of Object.entries(PLATFORM_PATTERNS)) {
        if (!seen.has(type) && regex.test(u)) {
          url.push({
            company_id: companyId,
            url: u,
            url_type: type,
          });
          seen.add(type);
        }
      }
    }

    const rawWebsite = entry.field_website?.[0]?.url;
    if (rawWebsite && !seen.has('WEBSITE')) {
      url.push({
        company_id: companyId,
        url: rawWebsite,
        url_type: 'WEBSITE',
      });
    }

    finalOutput.push({
      id: companyId,
      company,
      contact,
      category,
      taxonomy: taxonomyList,
      url,
    });
  });
  console.log(`Total contacts processed: ${totalContact}`);
  return finalOutput;
}

function splitArrayIntoChunks(array, chunkSize = 100) {
  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

module.exports = { processJsonDataHelper, splitArrayIntoChunks };
