const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { ICON_CONFIG } = require('../config/constants');

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

function getIconUrl(iconName) {
  if (!iconName) {
    return `${ICON_CONFIG.BASE_URL}/assets/icons/${ICON_CONFIG.DEFAULT_ICON}`;
  }

  // If iconName already contains a full URL, return it as is
  if (iconName.startsWith('http://') || iconName.startsWith('https://')) {
    return iconName;
  }

  // If iconName is a relative path starting with /assets, add the base URL
  if (iconName.startsWith('/assets/')) {
    return `${ICON_CONFIG.BASE_URL}${iconName}`;
  }

  // Ensure the icon has an extension
  const iconWithExt = iconName.endsWith('.png') ? iconName : `${iconName}.png`;

  return `${ICON_CONFIG.BASE_URL}/assets/icons/${iconWithExt}`;
}

function deleteIconFile(iconName) {
  const iconPath = path.join(ICON_CONFIG.ICONS_DIR, iconName);
  fs.unlink(iconPath, (err) => {
    if (err) {
      console.error('Error deleting icon file:', err);
      return false;
    }
  });
  return true;
}

// const getAvailableIcons = async () => {
//   try {
//     const files = await readdir(config.iconsDir);
//     return files.filter(file => file.endsWith('.svg'));
//   } catch (error) {
//     console.error('Error reading icons directory:', error);
//     return [];
//   }
// };

// const iconExists = async (iconName) => {
//   if (!iconName) return false;

//   const iconWithExt = iconName.endsWith('.svg') ? iconName : `${iconName}.svg`;
//   const iconPath = path.join(config.iconsDir, iconWithExt);

//   try {
//     const stats = await stat(iconPath);
//     return stats.isFile();
//   } catch (error) {
//     return false;
//   }
// };

// const getCategoryIconUrl = async (category) => {
//   // If the category already has an icon_url, use it
//   if (category.icon_url) {
//     return getIconUrl(category.icon_url);
//   }

//   // Try to find an icon with the same name as the category
//   const categoryName = category.category_name || '';
//   const normalizedName = categoryName.toLowerCase().replace(/\s+/g, '_');

//   if (await iconExists(normalizedName)) {
//     return getIconUrl(normalizedName);
//   }

//   // Fall back to the default icon
//   return getIconUrl(config.defaultIcon);
// };

module.exports = {
  getIconUrl,
  deleteIconFile,
  // getAvailableIcons,
  // iconExists,
  // getCategoryIconUrl,
};
