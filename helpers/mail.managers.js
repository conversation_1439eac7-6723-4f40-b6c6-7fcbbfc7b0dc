const nodemailer = require('nodemailer');
const ejs = require('ejs');
const path = require('path');
const fs = require('fs');
const constants = require('../config/constants');
const logoImage = path.resolve(__dirname, '../assets/images/icc-logo.png');

// Read the logo image as a buffer

module.exports = class {
  resetPasswordOtp = (receiver, otp, data = '') => {
    return new Promise((resolve, reject) => {
      ejs.renderFile(
        path.resolve(
          __dirname,
          `../${constants.EMAIL_TEMPLATES_PATH}/reset-password.ejs`
        ),
        { otp, data },
        (err, htmlStr) => {
          if (err) {
            console.log('EJS rendering error:', err);
            reject(err);
            return;
          }

          const transporter = nodemailer.createTransport({
            service: process.env.EMAIL_SERVICE,
            auth: {
              user: process.env.EMAIL_ID,
              pass: process.env.EMAIL_PASSWORD,
            },
          });

          const mailOptions = {
            from: process.env.EMAIL_FROM,
            to: receiver,
            subject: `Reset Password Email Verification`,
            html: htmlStr,
            attachments: [
              {
                filename: 'icc-logo.png',
                path: logoImage,
                cid: 'iccLogo',       // Reference the cid in the HTML
              }
            ]
          };

          transporter.sendMail(mailOptions, function (error, info) {
            if (error) {
              console.log('TCL -> error', error);
            }
          });

          resolve();
          return;
        }
      );
    });
  };
};
