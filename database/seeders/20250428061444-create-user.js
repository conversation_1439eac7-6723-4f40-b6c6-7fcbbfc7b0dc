'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */
    await queryInterface.bulkInsert('users', [
      {
        user_name: '<PERSON><PERSON><PERSON>',
        user_email: '<EMAIL>',
        user_password:
          '$2b$10$ENL.TWnUCywkJwDTu4U1QeqoJE7R08YQj5vr4e9WbQK1twZfim0dW',
        user_role: 'ADMIN',
      },
    ]);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    await queryInterface.bulkDelete('users', null, {});
  }
};
