'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('companies', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      company_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      parent_company: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      company_email: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      company_logo_url: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      company_country: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      company_address: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      company_website: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      upvote_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      downvote_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal(
          'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ),
      },
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('companies');
  }
};
