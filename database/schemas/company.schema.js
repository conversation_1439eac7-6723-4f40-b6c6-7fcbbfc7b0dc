module.exports = (sequelize, DataTypes) => {
  const Company = sequelize.define(
    'Company',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      comp_id: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: true,
      },
      company_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      parent_company: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      company_email: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      company_logo_url: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      company_country: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      company_address: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      company_website: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      last_updated_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      upvote_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      downvote_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
    },
    {
      tableName: 'companies',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Associations
  Company.associate = (models) => {
    Company.belongsToMany(models.Category, {
      through: models.CompanyCategory,
      foreignKey: 'company_id',
      otherKey: 'category_id',
      as: 'categories',
    });

    Company.hasMany(models.ContactNumber, {
      foreignKey: 'company_id',
      as: 'contact_numbers',
    });

    Company.hasMany(models.CompanyUrl, {
      foreignKey: 'company_id',
      as: 'company_urls',
    });

    Company.hasMany(models.CompanyCategory, {
      foreignKey: 'company_id',
      as: 'company_categories',
    });
  };

  return Company;
};
