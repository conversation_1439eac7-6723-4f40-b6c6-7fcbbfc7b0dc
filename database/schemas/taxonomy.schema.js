module.exports = (sequelize, DataTypes) => {
  const Taxonomy = sequelize.define(
    'Taxonomy',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      taxonomy_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      taxonomy_description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      taxonomy_weight: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      taxonomy_weight_unused: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      tableName: 'taxonomy',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Associations
  Taxonomy.associate = (models) => {
    Taxonomy.belongsToMany(models.Company, {
      through: 'CompanyTaxonomies',
      foreignKey: 'taxonomy_id',
      otherKey: 'company_id',
    });

    Taxonomy.hasMany(models.CompanyTaxonomy, {
      foreignKey: 'taxonomy_id',
      as: 'company_taxonomy',
    });
  };

  return Taxonomy;
};
