module.exports = (sequelize, DataTypes) => {
    const UserToken = sequelize.define(
      'UserToken',
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        user_id: {
          type: DataTypes.INTEGER,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        token: {
          type: DataTypes.STRING,
          allowNull: false,
        },
      },
      {
        tableName: 'user_tokens',
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      }
    );
  
    return UserToken;
  };
  