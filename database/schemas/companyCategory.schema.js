module.exports = (Sequelize, DataTypes) => {
  const CompanyCategory = Sequelize.define(
    'CompanyCategory',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      company_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'companies',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      category_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'categories',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
    },
    {
      tableName: 'company_categories',
      timestamps: false,
      indexes: [
        {
          unique: true,
          fields: ['company_id', 'category_id'],
        },
      ],
    }
  );

  // Associations
  CompanyCategory.associate = (models) => {
    CompanyCategory.belongsTo(models.Company, {
      foreignKey: 'company_id',
    });
    CompanyCategory.belongsTo(models.Category, {
      foreignKey: 'category_id',
    });
  };

  return CompanyCategory;
};
