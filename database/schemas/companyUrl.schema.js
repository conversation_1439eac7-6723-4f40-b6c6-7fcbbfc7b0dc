const { URL_TYPES } = require('../../config/constants');

module.exports = (sequelize, DataTypes) => {
  const CompanyUrl = sequelize.define(
    'CompanyUrl',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      company_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'companies',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      url: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      url_type: {
        type: DataTypes.ENUM(Object.values(URL_TYPES)),
        allowNull: false,
      },
    },
    {
      tableName: 'company_urls',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Associations
  CompanyUrl.associate = (models) => {
    CompanyUrl.belongsTo(models.Company, {
      foreignKey: 'company_id',
      targetKey: 'id',
    });
  };

  return CompanyUrl;
};
