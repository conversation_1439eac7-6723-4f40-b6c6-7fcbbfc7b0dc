const { CONTACT_TYPES } = require('../../config/constants');

module.exports = (sequelize, DataTypes) => {
  const ContactNumber = sequelize.define(
    'ContactNumber',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      company_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'companies',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      contact_number: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      contact_description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      contact_type: {
        type: DataTypes.ENUM(Object.values(CONTACT_TYPES)),
        allowNull: false,
      },
      upvote_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      downvote_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      is_whatsapp: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      tableName: 'contact_numbers',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Associations
  ContactNumber.associate = (models) => {
    ContactNumber.belongsTo(models.Company, {
      foreignKey: 'company_id',
    });
  };

  return ContactNumber;
};
