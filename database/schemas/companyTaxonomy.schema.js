module.exports = (sequelize, DataTypes) => {
  const CompanyTaxonomy = sequelize.define(
    'CompanyTaxonomy',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      company_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'companies',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      taxonomy_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'taxonomy',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
    },
    {
      tableName: 'company_taxonomy',
      timestamps: false,
    }
  );

  // Associations
  CompanyTaxonomy.associate = (models) => {
    CompanyTaxonomy.belongsTo(models.Company, {
      foreignKey: 'company_id',
    });
    CompanyTaxonomy.belongsTo(models.Taxonomy, {
      foreignKey: 'taxonomy_id',
    });
  };

  return CompanyTaxonomy;
};
