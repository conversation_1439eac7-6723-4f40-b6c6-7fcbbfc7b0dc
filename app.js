const express = require("express");
const app = express();
const dotenv = require("dotenv");
const cors = require("cors");
dotenv.config();
const logger = require('morgan');

require('./config/globals.js');

// Middleware to parse JSON
app.use(express.json({ limit: '1mb' }));

// Configure CORS based on environment
const corsOptions = {
  credentials: true, // If using cookies or authorization headers
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
};

if (process.env.NODE_ENV === 'production') {
  corsOptions.origin = [process.env.CMS_URL]; // Add more production URLs as needed
} else {
  corsOptions.origin = '*'; // Allow all origins in development
}

app.use(cors(corsOptions));
app.use(logger('dev'));

// Serve static files from the assets folder
app.use('/assets', express.static('assets'));

// ------- <PERSON> Handler --------- //
app.use((req, res, next) => {
  const ResponseHandler = require('./config/responseHandler.js');
  res.handler = new ResponseHandler(req, res);
  next();
});

// ------ Routes --------- //
const appRoutes = require('./routes/index.js');

appRoutes(app);

// -------   GLOBAL ERROR HANDLER --------- //
app.use((err, req, res, next) => {
  if (res.headersSent) {
    return next(err);
  }
  if (!res.handler) {
    const ResponseHandler = require('./config/responseHandler.js');
    res.handler = new ResponseHandler(req, res);
    console.log('Global Error Handler:', err);
    return res.handler.serverError(err);
  }

  return res.handler.serverError(err);
});

// Initialize crons when server starts
const { initializeCrons } = require('./crons/init.cron');
initializeCrons();

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
