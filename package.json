{"name": "api-customer-care", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node app.js", "dev": "nodemon app.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.0", "node-cron": "^4.1.0", "nodemailer": "^7.0.2", "pg": "^8.14.1", "sequelize": "^6.37.6", "sequelize-cli": "^6.6.2", "ws": "^8.18.2"}, "devDependencies": {"nodemon": "^3.1.10"}}