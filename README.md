# Indian Customer Care API

## Description

The customer care numbers and its management based API project

## Installation

1. Clone the repository:

```
git clone https://gitlab.openxcell.dev/a-team/india-customer-care/api-customer-care.git
```
2. Navigate to the project directory:

```
cd api-customer-care
```
3. Install dependencies:

```
npm install
```

## Usage

Start the server in development mode:

```
npm run dev
```

Or start the production server:

```
npm start
```

## Project Structure

```
└── 📁api-customer-care
    └── 📁config
    └── 📁controllers
    └── 📁database
        └── 📁migrations
        └── 📁schemas
        └── 📁seeders
    └── 📁middlewares
    └── 📁routes
        └── 📁V1
    └── 📁services
    └── .env
    └── .gitignore
    └── .sequelizerc
    └── app.js
    └── package.json
    └── README.md
```

## Tech Stack

- Node.js v20.18.0
- Express.js v4.21.2
- Sequelize v6.37.6
- MySQL v8.0.33