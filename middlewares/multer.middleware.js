const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { ICON_FIELD_NAME } = require('../config/constants');

// Middleware generator
function Multer(maxSize, allowedTypes, fieldName) {
  const uploadDir = 'assets/icons';
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  // Configure storage
  const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, 'assets/icons');
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
      cb(null, uniqueSuffix + path.extname(file.originalname));
    },
  });

  // File filter based on allowed extensions
  const fileFilter = (req, file, cb) => {
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      const multerErr = new multer.MulterError(
        'LIMIT_UNEXPECTED_FILE',
        ICON_FIELD_NAME
      );
      multerErr.message = `Invalid file type. Allowed: ${allowedTypes.join(
        ', '
      )}`;
      cb(multerErr);
    }
  };

  // Multer instance with dynamic config
  const upload = multer({
    storage,
    limits: { fileSize: maxSize },
    fileFilter,
  }).single(fieldName);

  // Return a middleware that runs multer and handles errors
  return (req, res, next) => {
    upload(req, res, (err) => {
      if (err instanceof multer.MulterError) {
        let message = err.message;

        // Customize messages for known multer error codes
        if (err.code === 'LIMIT_FILE_SIZE') {
          message = `File too large. Max allowed size is ${
            maxSize / (1024 * 1024)
          }MB.`;
        } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
          message = err.message || 'Unexpected file type.';
        }

        return res.handler.badRequest(message, null);
      } else if (err) {
        console.error('File upload error:', err);
        // Handle any other error
        return res.handler.badRequest(err.message, null);
      }

      next(); // Proceed if no error
    });
  };
}

function deleteUploadedFile(req) {
  if (req.file) {
    const filePath = req.file.path;
    fs.unlink(filePath, (err) => {
      if (err) console.error('Error deleting file:', err);
      else console.log('Uploaded file deleted due to validation/auth error.');
    });
  }
}

module.exports = { Multer, deleteUploadedFile };
