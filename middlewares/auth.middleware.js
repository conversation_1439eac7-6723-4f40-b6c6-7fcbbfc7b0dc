const jwt = require('jsonwebtoken');

const { validationResult } = require('express-validator');
const { JWT } = require('../config/constants');
const { API } = require('../config/message');
const { deleteUploadedFile } = require('./multer.middleware');
const AuthService = new (require('../services/auth.service'))();

exports.authentication = async (request, response, next) => {
  const errors = validationResult(request).formatWith(({ msg }) => msg);

  if (!errors.isEmpty()) {
    if (errors.array()?.length) {
      console.log(
        '=================================================================\n'
      );
      console.log(
        'authentication ~ request.originalUrl: ',
        request.originalUrl
      );
      console.log('authentication ~ request.method: ', request.method);
      console.log('authentication ~ request.query: ', request.query);
      console.log('authentication ~ request.body: ', request.body);
      console.log('authentication ~ errors.array(): ', errors.array());
      console.log(
        '=================================================================\n'
      );
      console.log('\n');
    }
    deleteUploadedFile(request);
    return response.handler.badRequest(undefined, errors.array());
  }

  if (request.originalUrl.includes('/app/v1')) {
    return next();
  }

  // USED FOR WHEN AUTHENTICATION IS OPTIONAL
  const publicPaths = [
    '/cms/v1/auth/signUp',
    '/cms/v1/auth/login',
    '/cms/v1/auth/verify-otp',
    // '/cms/v1/auth/resendOtp',
    '/cms/v1/auth/forgot-password',
    '/cms/v1/auth/reset-password',
  ];

  if (publicPaths.some((path) => request.originalUrl.includes(path))) {
    return next();
  }

  try {
    const token = request.headers.authorization.split(' ')[1];

    let getAuthDetails = jwt.verify(token, JWT.SECRET, {
      algorithms: [JWT.ALGORITHM],
    });

    let getUserAuthDetails = await AuthService.findUserToken({
      user_id: getAuthDetails.userId,
      token: token,
    });

    if (getUserAuthDetails) {
      request.userId = getUserAuthDetails.user_id;
      next();
    } else {
      console.log('User authentication failed: Invalid token');
      deleteUploadedFile(request);
      return response.handler.unauthorized(API.INVALID_TOKEN);
    }
  } catch (error) {
    deleteUploadedFile(request);
    switch (error.name) {
      case 'TokenExpiredError':
        return response.handler.unauthorized(API.TOKEN_EXPIRED);

      case 'JsonWebTokenError':
        if (error.message === 'invalid algorithm') {
          break;
        }

        return response.handler.unauthorized(API.INVALID_TOKEN);
      default:
        console.log(error);
        response.handler.serverError(error);
        break;
    }
  }
};
