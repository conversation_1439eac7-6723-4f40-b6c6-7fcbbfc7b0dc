const { body, param } = require('express-validator');
const { headerValidator } = require('./common.validator');

exports.createStaticPageValidator = [
  ...headerValidator,
  body('title')
    .trim()
    .notEmpty()
    .withMessage('Title is required')
    .isString()
    .withMessage('Title must be a string')
    .isLength({ min: 3, max: 100 })
    .withMessage('Title must be between 3 and 100 characters'),
  body('slug')
    .optional()
    .trim()
    .isString()
    .withMessage('Slug must be a string')
    .matches(/^[a-z0-9-]+$/)
    .withMessage('Slug can only contain lowercase letters, numbers, and hyphens')
    .isLength({ min: 3, max: 100 })
    .withMessage('Slug must be between 3 and 100 characters'),
  body('content')
    .trim()
    .notEmpty()
    .withMessage('Content is required')
    .isString()
    .withMessage('Content must be a string'),
];

exports.updateStaticPageValidator = [
  ...headerValidator,
  param('id')
    .isInt({ min: 1 })
    .withMessage('Invalid page ID'),
  body('title')
    .optional()
    .trim()
    .isString()
    .withMessage('Title must be a string')
    .isLength({ min: 3, max: 100 })
    .withMessage('Title must be between 3 and 100 characters'),
  body('slug')
    .optional()
    .trim()
    .isString()
    .withMessage('Slug must be a string')
    .matches(/^[a-z0-9-]+$/)
    .withMessage('Slug can only contain lowercase letters, numbers, and hyphens')
    .isLength({ min: 3, max: 100 })
    .withMessage('Slug must be between 3 and 100 characters'),
  body('content')
    .optional()
    .trim()
    .isString()
    .withMessage('Content must be a string'),
];

exports.getStaticPageBySlugValidator = [
  ...headerValidator,
  param('slug')
    .trim()
    .notEmpty()
    .withMessage('Slug is required')
    .isString()
    .withMessage('Slug must be a string')
    .matches(/^[a-z0-9-]+$/)
    .withMessage('Slug can only contain lowercase letters, numbers, and hyphens'),
];

exports.deleteStaticPageValidator = [
  ...headerValidator,
  param('id')
    .isInt({ min: 1 })
    .withMessage('Invalid page ID'),
];
