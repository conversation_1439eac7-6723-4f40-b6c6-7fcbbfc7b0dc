const { body, query } = require('express-validator');

const createFeedbackValidator = [
  body('userName')
    .notEmpty()
    .withMessage('User name is required')
    .isString()
    .withMessage('User name must be a string'),
  body('userEmail')
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Invalid email format'),
  body('comment')
    .notEmpty()
    .withMessage('Comment is required')
    .isString()
    .withMessage('Comment must be a string'),
];

const getPaginatedFeedbacksValidator = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Limit must be a positive integer'),
  query('search')
    .optional()
    .isString()
    .withMessage('Search term must be a string'),
];

module.exports = {
  createFeedbackValidator,
  getPaginatedFeedbacksValidator,
};
