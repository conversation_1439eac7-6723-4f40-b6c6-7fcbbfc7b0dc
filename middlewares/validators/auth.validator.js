const { body } = require('express-validator');

const { headerValidator } = require('./common.validator');

exports.loginValidator = [
  body('email', 'Please provide email.')
    .trim()
    .notEmpty()
    .isEmail()
    .withMessage('Please provide valid email.'),
  body('password', 'Please provide password.').trim().notEmpty(),
];

exports.logoutValidator = [...headerValidator];

exports.forgotPasswordValidator = [
  body('email', 'Please provide email.')
    .trim()
    .notEmpty()
    .isEmail()
    .withMessage('Please provide valid email.'),
];

exports.verifyOtpValidator = [
  body('email', 'Please provide email.')
    .trim()
    .notEmpty()
    .isEmail()
    .withMessage('Please provide valid email.'),
  body('otp', 'Please provide OTP.')
    .trim()
    .notEmpty()
    .isLength({ min: 6, max: 6 })
    .withMessage('OTP must be 6 digits.')
    .isNumeric()
    .withMessage('OTP must contain only numbers.'),
];

exports.resetPasswordValidator = [
  body('email', 'Please provide email.')
    .trim()
    .notEmpty()
    .isEmail()
    .withMessage('Please provide valid email.'),
  // body('otp', 'Please provide OTP.')
  //   .trim()
  //   .notEmpty()
  //   .isLength({ min: 6, max: 6 })
  //   .withMessage('OTP must be 6 digits.')
  //   .isNumeric()
  //   .withMessage('OTP must contain only numbers.'),
  body('password', 'Please provide password.')
    .trim()
    .notEmpty()
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long.'),
  // .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
  // .withMessage(
  //   'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.'
  // ),
  body('confirmPassword', 'Please provide confirm password.')
    .trim()
    .notEmpty()
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Password and confirm password do not match.');
      }
      return true;
    }),
];
