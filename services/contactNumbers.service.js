const { options } = require('sequelize/lib/model');
const { ContactNumber } = require('../database/schemas');

class ContactNumbersService {
  numberInstance(number) {
    return {
      numberId: number.id,
      companyId: number.company_id,
      number: number.contact_number,
      description: number.contact_description || null,
      type: number.contact_type || null,
      upvoteCount: number.upvote_count,
      downvoteCount: number.downvote_count,
      isWhatsapp: number.is_whatsapp,
    };
  }

  async findNumber(filter) {
    return await ContactNumber.findOne({ where: filter, raw: false });
  }

  async findAllNumbers(filter = {}, options = {}) {
    return await ContactNumber.findAll({ where: filter, ...options });
  }

  async createNumber(data) {
    return await ContactNumber.create(data);
  }

  async createContactNumbers(data, options = {}) {
    return await ContactNumber.bulkCreate(data, { ...options });
  }

  async updateNumber(filter, data) {
    return await ContactNumber.update(data, { where: filter });
  }

  async deleteNumber(filter, options = {}) {
    return await ContactNumber.destroy({ where: filter, ...options });
  }

  async truncateAll(transaction) {
    return await ContactNumber.destroy({
      where: {},
      truncate: true,
      cascade: true,
      transaction,
    });
  }

  async bulkCreateContactNumbers(contactNumbers, options = {}) {
    return await ContactNumber.bulkCreate(contactNumbers, options);
  }

  async bulkUpdateVotes(votesData) {
    try {
      const updates = votesData.map((vote) => ({
        where: { id: vote.contactId },
        update: {
          upvote_count: vote.upVotes,
          downvote_count: vote.downVotes,
        },
      }));

      const results = await Promise.all(
        updates.map(({ where, update }) =>
          ContactNumber.update(update, { where })
        )
      );

      return results;
    } catch (error) {
      console.error('Error updating vote counts:', error);
      throw error;
    }
  }
}

module.exports = ContactNumbersService;
