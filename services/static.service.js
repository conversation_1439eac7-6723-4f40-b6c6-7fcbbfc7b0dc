const { StaticPage } = require('../database/schemas');
const { Op } = require('sequelize');

class StaticService {
    staticPageInstance(staticPage) {
        return {
            id: staticPage.id,
            title: staticPage.title,
            slug: staticPage.slug,
            content: staticPage.content,
            createdAt: new Date(staticPage.created_at).toUTCString(),
            // updatedAt: new Date(staticPage.updated_at).toISOString(),
        };
    }

    /**
     * Generate a slug from a title
     * @param {string} title - The title to convert to a slug
     * @returns {string} - The generated slug
     */
    generateSlug(title) {
        return title
            .toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with a single hyphen
            .trim(); // Trim leading/trailing spaces
    }

    /**
     * Ensure a slug is unique by appending a number if necessary
     * @param {string} slug - The original slug
     * @param {number} id - Optional ID to exclude from uniqueness check (for updates)
     * @returns {string} - A unique slug
     */
    async ensureUniqueSlug(slug, id = null) {
        let uniqueSlug = slug;
        let counter = 1;
        let exists = true;

        // Build the where condition, excluding the current page if id is provided
        const whereCondition = id
            ? { slug: uniqueSlug, id: { [Op.ne]: id } }
            : { slug: uniqueSlug };

        // Check if the slug exists
        exists = await StaticPage.findOne({ where: whereCondition });

        // If the slug exists, append a number and check again until we find a unique slug
        while (exists) {
            uniqueSlug = `${slug}-${counter}`;
            counter++;

            const newWhereCondition = id
                ? { slug: uniqueSlug, id: { [Op.ne]: id } }
                : { slug: uniqueSlug };

            exists = await StaticPage.findOne({ where: newWhereCondition });
        }

        return uniqueSlug;
    }

    async createStaticPage(data) {
        // If no slug is provided, generate one from the title
        if (!data.slug && data.title) {
            data.slug = this.generateSlug(data.title);
        }

        // Ensure the slug is unique
        data.slug = await this.ensureUniqueSlug(data.slug);

        // Create the static page
        const page = await StaticPage.create(data);
        return this.staticPageInstance(page);
    }

    async updateStaticPage(id, data) {
        const page = await StaticPage.findByPk(id);

        if (!page) {
            return null;
        }

        // If title is updated but slug isn't, regenerate the slug
        if (data.title && !data.slug) {
            data.slug = this.generateSlug(data.title);
        }

        // If slug is provided or generated, ensure it's unique
        if (data.slug) {
            data.slug = await this.ensureUniqueSlug(data.slug, id);
        }

        // Update the page
        await page.update(data);
        return this.staticPageInstance(page);
    }

    async getStaticPages() {
        const pages = await StaticPage.findAll({
            order: [['created_at', 'DESC']]
        });
        return pages.map(page => this.staticPageInstance(page));
    }

    async getStaticPageById(id) {
        const page = await StaticPage.findByPk(id);
        return page ? this.staticPageInstance(page) : null;
    }

    async getStaticPageBySlug(slug) {
        const page = await StaticPage.findOne({ where: { slug } });
        return page ? this.staticPageInstance(page) : null;
    }

    async deleteStaticPage(id) {
        const page = await StaticPage.findByPk(id);

        if (!page) {
            return false;
        }

        await page.destroy();
        return true;
    }
}

module.exports = StaticService;
