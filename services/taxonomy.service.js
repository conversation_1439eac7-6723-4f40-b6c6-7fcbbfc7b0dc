const { Taxonomy, CompanyTaxonomy } = require('../database/schemas');

class TaxonomyService {
  taxonomyInstance(taxonomy) {
    return {
      taxonomyId: taxonomy.id,
      name: taxonomy.name,
      description: taxonomy.description
    };
  }

  async findTaxonomy(filter) {
    return await Taxonomy.findOne({ where: filter, raw: false });
  }

  async findAllTaxonomies(filter = {}) {
    return await Taxonomy.findAll({ where: filter });
  }

  async createTaxonomy(data) {
    return await Taxonomy.create(data);
  }

  async updateTaxonomy(filter, data) {
    return await Taxonomy.update(data, { where: filter });
  }

  async deleteTaxonomy(filter) {
    return await Taxonomy.destroy({ where: filter });
  }

  async findOrCreateTaxonomiesBulk(taxonomies, transaction) {
    // First find all existing taxonomies
    const taxonomyNames = taxonomies.map(t => t.taxonomy_name);
    const existingTaxonomies = await Taxonomy.findAll({
      where: {
        taxonomy_name: taxonomyNames
      },
      transaction
    });
    
    // Determine which taxonomies need to be created
    const existingTaxonomyNames = new Set(existingTaxonomies.map(t => t.taxonomy_name));
    const taxonomiesToCreate = taxonomies.filter(t => !existingTaxonomyNames.has(t.taxonomy_name));
    
    // Create missing taxonomies
    let newTaxonomies = [];
    if (taxonomiesToCreate.length > 0) {
      newTaxonomies = await Taxonomy.bulkCreate(taxonomiesToCreate, {
        transaction,
        returning: true
      });
    }
    
    // Return all taxonomies (existing + newly created)
    return [...existingTaxonomies, ...newTaxonomies];
  }

  async bulkCreateLinks(links, transaction) {
    return await CompanyTaxonomy.bulkCreate(links, { transaction });
  }

  async truncateAll(transaction) {
    await CompanyTaxonomy.destroy({ where: {}, truncate: true, cascade: true, transaction });
    // Uncomment if you also want to truncate the Taxonomy table
    await Taxonomy.destroy({ where: {}, truncate: false, cascade: true, transaction });
  }
}

module.exports = TaxonomyService;
