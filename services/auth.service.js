const { User, UserToken, Otp } = require('../database/schemas');

class AuthService {
  userInstance(user) {
    return {
      // userId: user.id,
      userName: user.user_name,
      userEmail: user.user_email,
      userRole: user.user_role,
    };
  }

  async findUser(filter, options = {}) {
    return await User.findOne({ where: filter, raw: false, ...options });
  }

  async findUserToken(filter, options = {}) {
    return await UserToken.findOne({ where: filter, raw: false, ...options });
  }

  async findOrCreateUserToken(filter, data, options = {}) {
    return await UserToken.findOrCreate({
      where: filter,
      defaults: data,
      ...options,
    });
  }

  async updateUserToken(filter, data, options = {}) {
    return await UserToken.update(data, { where: filter, ...options });
  }

  async deleteUserToken(filter, options = {}) {
    return await UserToken.destroy({ where: filter, ...options });
  }

  // OTP Token methods
  async createOtpToken(data) {
    return await Otp.create(data);
  }

  async findOtpToken(filter, options = {}) {
    return await Otp.findOne({ where: filter, raw: false, ...options });
  }

  async updateOtpToken(filter, data, options = {}) {
    return await Otp.update(data, { where: filter, ...options });
  }

  async deleteOtpToken(filter, options = {}) {
    return await Otp.destroy({ where: filter, ...options });
  }

  async expireAllUserOtpTokens(userId) {
    return await Otp.update(
      { status: 'expired' },
      {
        where: {
          user_id: userId,
          status: 'unused',
        },
      }
    );
  }

  // Generate a random 6-digit OTP
  generateOtp() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
}

module.exports = AuthService;
