const { WEBSITE_URL } = require('../config/constants');
const { Company, Category } = require('../database/schemas');

class CompaniesService {
  companyInstance(company) {
    return {
      companyId: company.id,
      compId: company.comp_id,
      companyName: company.company_name || null,
      parentCompany: company.parent_company || null,
      companyEmail: company.company_email || null,
      companyLogoUrl: company.company_logo_url
        ? WEBSITE_URL + company.company_logo_url
        : null,
      companyCountry: company.company_country || null,
      companyAddress: company.company_address || null,
      companyWebsite: company.company_website || null,
      lastUpdateAt: new Date(company.last_updated_at).toUTCString() || null,
      upVoteCount: company.upvote_count || 0,
      downVoteCount: company.downvote_count || 0,
      createdAt: new Date(company.created_at).toUTCString() || null,
    };
  }

  async findPaginatedCompanies(filter, options = {}, categoryId = null) {
    const {
      page = 1,
      limit = 10,
      order = [
        ['created_at', 'DESC'],
        ['id', 'DESC'],
      ],
    } = options;
    const offset = (page - 1) * limit;

    // Ensure order is valid - filter out any invalid entries
    const validOrder = order.filter(
      (orderItem) =>
        Array.isArray(orderItem) &&
        orderItem.length === 2 &&
        orderItem[0] &&
        orderItem[1] &&
        typeof orderItem[0] === 'string' &&
        typeof orderItem[1] === 'string'
    );

    // If no valid order items, use default
    const finalOrder =
      validOrder.length > 0
        ? validOrder
        : [
            ['created_at', 'DESC'],
            ['id', 'DESC'],
          ];

    // Include category filter if categoryId is provided

    const { count, rows } = await Company.findAndCountAll({
      where: filter,
      // include,
      limit,
      offset,
      order: finalOrder,
      distinct: true,
      ...options,
    });

    return {
      total: count,
      page,
      limit,
      rawData: rows, // Include raw data for contact number access
    };
  }

  async findCompany(filter, options = {}) {
    return await Company.findOne({ where: filter, raw: false, ...options });
  }

  async findAllCompanies(filter = {}, options = {}) {
    return await Company.findAll({ where: filter, ...options });
  }

  async createCompany(data, options = {}) {
    return await Company.create(data, { ...options });
  }

  async updateCompany(filter, data) {
    return await Company.update(data, { where: filter });
  }

  async deleteCompany(filter, options = {}) {
    return await Company.destroy({ where: filter, ...options });
  }

  async truncateAll(transaction) {
    return await Company.destroy({
      where: {},
      truncate: false,
      cascade: true,
      transaction,
    });
  }

  async bulkCreateCompanies(companies, options = {}) {
    return await Company.bulkCreate(companies, options);
  }
}

module.exports = CompaniesService;
