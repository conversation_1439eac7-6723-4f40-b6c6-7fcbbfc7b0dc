const { CompanyUrl } = require('../database/schemas');

class CompanyUrlsService {
  urlInstance(url) {
    return {
      urlId: url.id,
      companyId: url.company_id,
      url: url.url,
      type: url.url_type,
      // isActive: url.is_active,
    };
  }

  async findUrl(filter) {
    return await CompanyUrl.findOne({ where: filter, raw: false });
  }

  async findAllUrls(filter = {}, options = {}) {
    return await CompanyUrl.findAll({ where: filter, ...options });
  }

  async createUrl(data) {
    return await CompanyUrl.create(data);
  }

  async createUrls(data, options = {}) {
    return await CompanyUrl.bulkCreate(data, { ...options });
  }

  async updateUrl(filter, data) {
    return await CompanyUrl.update(data, { where: filter });
  }

  async deleteUrl(filter, options = {}) {
    return await CompanyUrl.destroy({ where: filter, ...options });
  }

  async truncateAll(transaction) {
    return await CompanyUrl.destroy({
      where: {},
      truncate: true,
      cascade: true,
      transaction,
    });
  }

  async bulkCreateUrls(urls, options = {}) {
    return await CompanyUrl.bulkCreate(urls, options);
  }
}

module.exports = CompanyUrlsService;
