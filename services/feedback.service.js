const { Feedback } = require('../database/schemas');
const { Op } = require('sequelize');

class FeedbackService {
  feedbackInstance(feedback) {
    return {
      userName: feedback.user_name,
      userEmail: feedback.user_email,
      comment: feedback.comment,
      createdAt: feedback.created_at,
    };
  }

  async findAllFeedbacks(filter = {}, options = {}) {
    const { page = 1, limit = 10, search } = options;
    const offset = (page - 1) * limit;

    let whereClause = { ...filter };

    if (search) {
      whereClause = {
        ...whereClause,
        [Op.or]: [
          { user_name: { [Op.like]: `%${search}%` } },
          { user_email: { [Op.like]: `%${search}%` } },
          { comment: { [Op.like]: `%${search}%` } },
        ],
      };
    }

    const { count, rows } = await Feedback.findAndCountAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit,
      offset,
      ...options,
    });

    return {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      feedbacks: rows.map((feedback) => this.feedbackInstance(feedback)),
    };
  }

  async createFeedback(data) {
    const feedback = await Feedback.create(data);
    return this.feedbackInstance(feedback);
  }
}

module.exports = FeedbackService;
