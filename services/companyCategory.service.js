const { CompanyCategory, Company, Category } = require('../database/schemas');

class CompanyCategoryService {
  companyCategoryInstance(companyCategory) {
    return {
      id: companyCategory.id,
      companyId: companyCategory.company_id,
      categoryId: companyCategory.category_id,
    };
  }

  async findAllCompanyCategories(filter = {}, options = {}) {
    return await CompanyCategory.findAll({
      where: filter,
      ...options,
    });
  }

  async findCompanyCategory(filter, options = {}) {
    return await CompanyCategory.findOne({
      where: filter,
      raw: false,
      ...options,
    });
  }

  async findAllWithDetails(filter = {}, options = {}) {
    return await CompanyCategory.findAll({
      where: filter,
      include: [
        {
          model: Company,
          attributes: ['id', 'company_name'],
        },
        {
          model: Category,
          attributes: ['id', 'category_name', 'icon_url'],
        },
      ],
      ...options,
    });
  }

  async bulkCreateLinks(links, options = {}) {
    return await CompanyCategory.bulkCreate(links, options);
  }

  async findOrCreateLinks(links, options = {}) {
    const result = [];
    for (const link of links) {
      const [linksObj] = await CompanyCategory.findOrCreate({
        where: { company_id: link.company_id, category_id: link.category_id },
        defaults: { ...link },
        ...options,
      });
      result.push(linksObj);
    }
    return result;
  }

  async truncateAll(transaction) {
    return await CompanyCategory.destroy({
      where: {},
      truncate: true,
      cascade: true,
      transaction,
    });
  }
}

module.exports = CompanyCategoryService;
