const { STATUS_CODES } = require('../config/constants');
const { API } = require('../config/message');
const { sequelize } = require('../database/schemas');
const { Transaction } = require('sequelize'); // Import Transaction directly

const CompaniesService = new (require('../services/companies.service'))();
const ContactNumbersService =
  new (require('../services/contactNumbers.service'))();
const CategoriesService = new (require('../services/categories.service'))();
// const TaxonomyService = new (require('../services/taxonomy.service'))();
const CompanyUrlsService = new (require('../services/companyUrls.service'))();
const CompanyCategoryService =
  new (require('../services/companyCategory.service'))();

class DataController {
  async getAllCompanyCategories(_req, res) {
    try {
      const companyCategories =
        await CompanyCategoryService.findAllCompanyCategories();

      if (!companyCategories || companyCategories.length === 0) {
        return res.handler.custom(
          STATUS_CODES.NOT_FOUND,
          'No company-category relationships found',
          null
        );
      }

      const formattedCompanyCategories = companyCategories.map((cc) => ({
        id: cc.id,
        companyId: cc.company_id,
        categoryId: cc.category_id,
      }));

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        'Company-category relationships retrieved successfully',
        formattedCompanyCategories
      );
    } catch (error) {
      console.error('Error fetching company categories:', error);
      return res.handler.custom(
        STATUS_CODES.SERVER_ERROR,
        API.SERVER_ERROR,
        null,
        error
      );
    }
  }

  async bulkUploadData(req, res) {
    const { chunkedData: data, isFirstChunk } = req.body;
    let transaction;

    try {
      // 1. Add connection management with retry logic
      const MAX_RETRIES = 3;
      const RETRY_DELAY = 2000; // 2 seconds

      // Helper function to retry operations with connection errors
      const withRetry = async (operation, retries = MAX_RETRIES) => {
        try {
          return await operation();
        } catch (error) {
          if (
            retries > 0 &&
            (error.message.includes('max_user_connections') ||
              error.name === 'SequelizeConnectionError')
          ) {
            console.log(
              `Connection error, retrying in ${RETRY_DELAY}ms... (${retries} attempts left)`
            );
            await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
            return withRetry(operation, retries - 1);
          }
          throw error;
        }
      };

      // 2. Process in even smaller batches to reduce connection usage
      const BATCH_SIZE = 10; // Reduced from 20 to 10

      // 3. If this is the first chunk, truncate tables
      if (isFirstChunk === true) {
        await withRetry(async () => {
          const truncateTransaction = await sequelize.transaction({
            isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
          });

          try {
            await CompaniesService.truncateAll(truncateTransaction);
            await ContactNumbersService.truncateAll(truncateTransaction);
            await CategoriesService.truncateAll(truncateTransaction);
            // await TaxonomyService.truncateAll(truncateTransaction);
            await CompanyUrlsService.truncateAll(truncateTransaction);

            await truncateTransaction.commit();
          } catch (error) {
            await truncateTransaction.rollback();
            throw error;
          }
        });
      }

      // 4. Process data in smaller batches with connection management
      for (let i = 0; i < data.length; i += BATCH_SIZE) {
        const batchData = data.slice(i, i + BATCH_SIZE);

        await withRetry(async () => {
          const batchTransaction = await sequelize.transaction({
            isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
          });

          try {
            // Extract unique categories and taxonomies for this batch
            const uniqueCategories = new Map();
            // const uniqueTaxonomies = new Map();

            batchData.forEach((companyObj) => {
              companyObj.category.forEach((cat) => {
                uniqueCategories.set(cat.category_name, {
                  cat_id: cat.id,
                  category_name: cat.category_name,
                  icon_url: cat.icon_url || null,
                });
              });

              // companyObj.taxonomy.forEach(tax => {
              //   uniqueTaxonomies.set(tax.name, {
              //     taxonomy_name: tax.name,
              //     icon_url: tax.icon_url || null
              //   });
              // });
            });

            // Process categories and taxonomies
            const categoryMap = new Map();
            if (uniqueCategories.size > 0) {
              const categoryEntries =
                await CategoriesService.findOrCreateCategoriesBulk(
                  Array.from(uniqueCategories.values()),
                  batchTransaction
                );
              categoryEntries.forEach((cat) => {
                categoryMap.set(cat.category_name, cat);
              });
            }

            // const taxonomyMap = new Map();
            // if (uniqueTaxonomies.size > 0) {
            //   const taxonomyEntries = await TaxonomyService.findOrCreateTaxonomiesBulk(
            //     Array.from(uniqueTaxonomies.values()),
            //     batchTransaction
            //   );
            //   taxonomyEntries.forEach(tax => {
            //     taxonomyMap.set(tax.taxonomy_name, tax);
            //   });
            // }

            // Prepare company data
            const companyPayloads = batchData.map((companyObj) => ({
              comp_id: companyObj.company.id,
              company_name: companyObj.company.company_name,
              parent_company: companyObj.company.parent_company || null,
              company_email: companyObj.company.company_email || null,
              company_logo_url: companyObj.company.company_logo_url || null,
              company_country: companyObj.company.company_country || null,
              company_address: companyObj.company.company_address || null,
              company_website: companyObj.company.company_website || null,
              last_updated_at:
                new Date(
                  companyObj.company.last_updated_at * 1000
                ).toUTCString() || null,
            }));

            // Bulk create companies
            const createdCompanies = await CompaniesService.bulkCreateCompanies(
              companyPayloads,
              batchTransaction
            );

            // Prepare related data
            const contactPayloads = [];
            const categoryLinkPayloads = [];
            // const taxonomyLinkPayloads = [];
            const urlPayloads = [];

            createdCompanies.forEach((companyInstance, index) => {
              const companyId = companyInstance.id;
              // const { contact, category, taxonomy, url } = batchData[index];
              const { contact, category, url } = batchData[index];

              // Contact numbers
              contact.forEach((c) => {
                contactPayloads.push({
                  company_id: companyId,
                  contact_number: c.number,
                  contact_description: c.contact_description || null,
                  contact_type: c.contact_type,
                  is_whatsapp: c.is_whatsapp,
                });
              });

              // Category links
              category.forEach((cat) => {
                const categoryInstance = categoryMap.get(cat.category_name);
                if (categoryInstance) {
                  categoryLinkPayloads.push({
                    company_id: companyId,
                    category_id: categoryInstance.id,
                  });
                }
              });

              // Taxonomy links
              // taxonomy.forEach(tax => {
              //   const taxonomyInstance = taxonomyMap.get(tax.name);
              //   if (taxonomyInstance) {
              //     taxonomyLinkPayloads.push({
              //       company_id: companyId,
              //       taxonomy_id: taxonomyInstance.id
              //     });
              //   }
              // });

              // URLs
              url.forEach((u) => {
                urlPayloads.push({
                  company_id: companyId,
                  url: u.url,
                  url_type: u.url_type,
                });
              });
            });

            // Process related data in smaller chunks
            const RELATED_BATCH_SIZE = 50; // Reduced from 100

            if (contactPayloads.length > 0) {
              for (
                let j = 0;
                j < contactPayloads.length;
                j += RELATED_BATCH_SIZE
              ) {
                await ContactNumbersService.bulkCreateContactNumbers(
                  contactPayloads.slice(j, j + RELATED_BATCH_SIZE),
                  batchTransaction
                );
              }
            }

            if (categoryLinkPayloads.length > 0) {
              for (
                let j = 0;
                j < categoryLinkPayloads.length;
                j += RELATED_BATCH_SIZE
              ) {
                await CategoriesService.bulkCreateLinks(
                  categoryLinkPayloads.slice(j, j + RELATED_BATCH_SIZE),
                  batchTransaction
                );
              }
            }

            // if (taxonomyLinkPayloads.length > 0) {
            //   for (
            //     let j = 0;
            //     j < taxonomyLinkPayloads.length;
            //     j += RELATED_BATCH_SIZE
            //   ) {
            //     await TaxonomyService.bulkCreateLinks(
            //       taxonomyLinkPayloads.slice(j, j + RELATED_BATCH_SIZE),
            //       batchTransaction
            //     );
            //   }
            // }

            if (urlPayloads.length > 0) {
              for (let j = 0; j < urlPayloads.length; j += RELATED_BATCH_SIZE) {
                await CompanyUrlsService.bulkCreateUrls(
                  urlPayloads.slice(j, j + RELATED_BATCH_SIZE),
                  batchTransaction
                );
              }
            }

            // Commit this batch's transaction
            await batchTransaction.commit();
            console.log(
              `Processed batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(
                data.length / BATCH_SIZE
              )}`
            );
          } catch (error) {
            // Rollback this batch's transaction if there's an error
            await batchTransaction.rollback();
            throw error;
          }
        });

        // Add a small delay between batches to allow connections to be released
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        'Data uploaded successfully',
        null,
        null
      );
    } catch (error) {
      if (transaction) await transaction.rollback();
      console.error('Error in bulkUploadData:', error);
      return res.handler.custom(
        STATUS_CODES.SERVER_ERROR,
        'An error occurred',
        null,
        error
      );
    }
  }
}

module.exports = DataController;
