const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const { STATUS_CODES, JWT, OTP } = require('../config/constants');
const { API } = require('../config/message');
// const { User } = require('../models');
const AuthService = new (require('../services/auth.service'))();
const MailManager = new (require('../helpers/mail.managers'))();

class AuthController {
  async login(req, res) {
    const body = req.body;

    try {
      // Check if user exists
      const user = await AuthService.findUser({
        user_email: body.email,
      });

      if (!user) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.ADMIN_NOT_FOUND);
      }

      // Compare entered password with stored hashed password
      const validPassword = await bcrypt.compare(
        body.password,
        user.user_password
      );

      if (!validPassword) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.LOGIN_FAILED);
      }

      // Generate JWT Token
      const jwtToken = jwt.sign(
        { userId: user.id, username: user.user_email },
        JWT.SECRET,
        {
          expiresIn: JWT.EXPIRES_IN, // Token expires in 1 day
          algorithm: JWT.ALGORITHM,
        }
      );

      const tokenPayload = {
        user_id: user.id,
        token: jwtToken,
      };

      const [userToken, created] = await AuthService.findOrCreateUserToken(
        { user_id: user.id },
        tokenPayload
      );

      if (!created) {
        // Update existing token
        userToken.token = jwtToken;

        await userToken.save();
      }

      return res.handler.success(API.LOGIN_SUCCESS, {
        token: userToken.token,
        ...AuthService.userInstance(user),
      });
    } catch (error) {
      console.error('Login error:', error);
      return res.handler.serverError();
    }
  }

  async forgotPassword(req, res) {
    try {
      const { email } = req.body;

      // Check if user exists
      const user = await AuthService.findUser({
        user_email: email,
      });

      if (!user) {
        return res.handler.notFound(API.FORGOT_PASSWORD_FAILED);
      }

      // Delete all existing unused User tokens for this user
      await AuthService.deleteUserToken({ user_id: user.id });

      // Generate a new OTP
      const otp = AuthService.generateOtp();

      // Calculate expiry time (30 minutes from now)
      const expiryTime = new Date();
      expiryTime.setMinutes(expiryTime.getMinutes() + OTP.EXPIRY_MINUTES);

      const existingOtp = await AuthService.findOtpToken({
        user_id: user.id,
      });

      if (existingOtp) {
        // Update existing OTP
        existingOtp.token = otp;
        existingOtp.expires_at = expiryTime;

        await existingOtp.save();
      } else {
        // Create new OTP
        await AuthService.createOtpToken({
          user_id: user.id,
          token: otp,
          expires_at: expiryTime,
        });
      }

      // Send email with OTP
      await MailManager.resetPasswordOtp(email, otp);

      return res.handler.success(API.FORGOT_PASSWORD_SUCCESS);
    } catch (error) {
      console.error('Forgot password error:', error);
      return res.handler.serverError();
    }
  }

  async verifyOtp(req, res) {
    try {
      const { email, otp } = req.body;

      // Check if user exists
      const user = await AuthService.findUser({
        user_email: email,
      });

      if (!user) {
        return res.handler.notFound(API.USER_NOT_FOUND);
      }

      // Find the OTP token
      const otpToken = await AuthService.findOtpToken({
        user_id: user.id,
        token: otp,
      });

      if (!otpToken) {
        return res.handler.badRequest(API.INVALID_OTP);
      }

      // Check if OTP is expired
      const now = new Date();
      if (now > otpToken.expires_at) {
        // Delete token as expired
        await AuthService.deleteUserToken({ id: otpToken.id });
        return res.handler.badRequest(API.EXPIRED_OTP);
      }

      // Delete token as used
      await AuthService.deleteUserToken({ id: otpToken.id });

      // OTP is valid
      return res.handler.success('OTP verified successfully');
    } catch (error) {
      console.error('Verify OTP error:', error);
      return res.handler.serverError();
    }
  }

  async resetPassword(req, res) {
    try {
      const { email, otp, password } = req.body;

      // Check if user exists
      const user = await AuthService.findUser({
        user_email: email,
      });

      if (!user) {
        return res.handler.notFound(API.USER_NOT_FOUND);
      }

      // Find the OTP token
      // const otpToken = await AuthService.findOtpToken({
      //   user_id: user.id,
      //   token: otp,
      // });

      // if (!otpToken) {
      //   return res.handler.badRequest(API.INVALID_OTP);
      // }

      // Check if OTP is expired
      // const now = new Date();
      // if (now > otpToken.expires_at) {
      //   // Update token status to expired
      //   await AuthService.deleteUserToken({ id: otpToken.id });
      //   return res.handler.badRequest(API.EXPIRED_OTP);
      // }

      // Hash the new password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      // Update user's password
      await user.update({ user_password: hashedPassword });

      // Mark OTP as used
      // await AuthService.deleteUserToken({ id: otpToken.id });

      return res.handler.success(API.RESET_PASSWORD_SUCCESS);
    } catch (error) {
      console.error('Reset password error:', error);
      return res.handler.serverError();
    }
  }

  async logout(req, res) {
    try {
      const token = req.headers.authorization.split(' ')[1];
      const userId = req.userId;

      // Delete the token from user_tokens table
      await AuthService.deleteUserToken({
        user_id: userId,
        token: token,
      });

      return res.handler.success(API.LOGOUT_SUCCESS);
    } catch (error) {
      return res.handler.serverError();
    }
  }
}

module.exports = AuthController;
