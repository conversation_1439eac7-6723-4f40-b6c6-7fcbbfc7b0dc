const { STATUS_CODES } = require('../config/constants');
const { API } = require('../config/message');
const { validationResult } = require('express-validator');

const FeedbackService = new (require('../services/feedback.service'))();

class FeedbackController {
  async createFeedback(req, res) {
    try {
      const data = req.body;

      const payload = {
        user_name: data.userName,
        user_email: data.userEmail,
        comment: data.comment,
      };

      const result = await FeedbackService.createFeedback(payload);

      res.handler.custom(STATUS_CODES.CREATED, API.SUCCESS, result);
    } catch (error) {
      console.error('Error creating feedback:', error);
      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }

  async getAllFeedbacks(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const search = req.query.search?.trim();

      const result = await FeedbackService.findAllFeedbacks(
        {},
        {
          page,
          limit,
          ...(search && { search }),
        }
      );

      if (!result || !result.feedbacks.length) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.NOT_FOUND);
      }

      res.handler.custom(STATUS_CODES.SUCCESS, API.SUCCESS, result);
    } catch (error) {
      console.error('Error fetching feedbacks:', error);
      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }
}

module.exports = FeedbackController;
