const { Op } = require('sequelize');
const { STATUS_CODES, CONTACT_TYPES } = require('../config/constants');
const { API } = require('../config/message');
const CategoriesService = new (require('../services/categories.service'))();
const ContactNumbersService =
  new (require('../services/contactNumbers.service'))();

const CompaniesService = new (require('../services/companies.service'))();
const CompanyUrlsService = new (require('../services/companyUrls.service'))();
const { Category } = require('../database/schemas');
const { WebSocket } = require('ws');

class CompanyController {
  async getPaginatedCompaniesByCategory(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'created_at',
        sortOrder = 'DESC',
        categoryId,
        search = '',
        lastDateTime,
      } = req.query;
      const filter = {};

      // Validate and set default sorting
      const validSortFields = [
        'id',
        'company_name',
        'created_at',
        'updated_at',
        'upvote_count',
        'downvote_count',
      ];
      const finalSortBy = validSortFields.includes(sortBy)
        ? sortBy
        : 'created_at';
      const finalSortOrder = ['ASC', 'DESC'].includes(sortOrder?.toUpperCase())
        ? sortOrder.toUpperCase()
        : 'DESC';

      const include = [
        {
          association: 'categories',
          required: !!categoryId,
          ...(categoryId && { where: { id: categoryId } }),
          order: [['id', 'ASC']],
          as: 'categories',
        },
        {
          association: 'contact_numbers',
          required: false,
          order: [['id', 'ASC']],
        },
        {
          association: 'company_urls',
          required: false,
          order: [['id', 'ASC']],
        },
      ];

      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        order: [[finalSortBy, finalSortOrder]],
        include,
        logging: false,
      };

      if (search) {
        filter.company_name = { [Op.like]: `%${search.toLowerCase()}%` };
      }

      if (lastDateTime) {
        const lastDate = new Date(lastDateTime * 1000);
        if (isNaN(lastDate.getTime())) {
          return res.handler.custom(
            STATUS_CODES.BAD_REQUEST,
            API.INVALID_DATE_TIME
          );
        }
        filter.last_updated_at = { [Op.gt]: lastDate };
      }

      const category = await CategoriesService.findCategory({ id: categoryId });

      if (!category) {
        return res.handler.custom(
          STATUS_CODES.NOT_FOUND,
          API.CATEGORY_NOT_FOUND
        );
      }

      const result = await CompaniesService.findPaginatedCompanies(
        filter,
        options,
        categoryId
      );

      // Process the raw data to format companies with their first contact number
      const formattedCompanies = result.rawData.map((company) => {
        // Format the company using the companyInstance method
        const formattedCompany = CompaniesService.companyInstance(company);

        const groupedContacts = {
          TOLL_FREE: [],
          ALL_INDIA: [],
          INTERNATIONAL: [],
        };

        for (const contact of company.contact_numbers) {
          const formattedContact =
            ContactNumbersService.numberInstance(contact);
          switch (contact.contact_type) {
            case CONTACT_TYPES.TOLL_FREE:
              groupedContacts.TOLL_FREE.push(formattedContact);
              break;
            case CONTACT_TYPES.ALL_INDIA:
              groupedContacts.ALL_INDIA.push(formattedContact);
              break;
            case CONTACT_TYPES.INTERNATIONAL:
              groupedContacts.INTERNATIONAL.push(formattedContact);
              break;
          }
        }

        return {
          ...formattedCompany,
          categories: company.categories.map(
            CategoriesService.categoryInstance
          ),
          number: groupedContacts,
          companyUrls: company.company_urls.map(CompanyUrlsService.urlInstance),
        };
      });

      // Prepare the response following the established API pattern
      const response = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        companies: formattedCompanies,
      };

      res.handler.custom(STATUS_CODES.SUCCESS, API.COMPANIES_FETCHED, response);
    } catch (error) {
      console.log('Error fetching paginated companies:', error);

      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }

  async getAllPaginatedCompanies(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'created_at',
        sortOrder = 'DESC',
        search = '',
        lastDateTime,
      } = req.query;
      const filter = {};

      // Validate and set default sorting
      const validSortFields = [
        'id',
        'company_name',
        'created_at',
        'updated_at',
        'upvote_count',
        'downvote_count',
      ];
      const finalSortBy = validSortFields.includes(sortBy)
        ? sortBy
        : 'created_at';
      const finalSortOrder = ['ASC', 'DESC'].includes(sortOrder?.toUpperCase())
        ? sortOrder.toUpperCase()
        : 'DESC';

      const include = [
        {
          association: 'categories',
          required: false,
          order: [['id', 'ASC']],
          as: 'categories',
        },
        {
          association: 'contact_numbers',
          required: false,
          order: [['id', 'ASC']],
        },
        {
          association: 'company_urls',
          required: false,
          order: [['id', 'ASC']],
        },
      ];

      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        // order: [[sortBy, sortOrder]],
        order: [[finalSortBy, finalSortOrder]],
        include,
        logging: false,
      };

      if (search) {
        filter.company_name = { [Op.like]: `%${search.toLowerCase()}%` };
      }

      if (lastDateTime) {
        const lastDate = new Date(lastDateTime * 1000);
        if (isNaN(lastDate.getTime())) {
          return res.handler.custom(
            STATUS_CODES.BAD_REQUEST,
            API.INVALID_DATE_TIME
          );
        }
        filter.last_updated_at = { [Op.gt]: lastDate };
      }

      const result = await CompaniesService.findPaginatedCompanies(
        filter,
        options
      );

      const formattedCompanies = result.rawData.map((company) => {
        // Format the company using the companyInstance method
        const formattedCompany = CompaniesService.companyInstance(company);

        // Add the first contact number if available
        const groupedContacts = {
          TOLL_FREE: [],
          ALL_INDIA: [],
          INTERNATIONAL: [],
        };

        for (const contact of company.contact_numbers) {
          const formattedContact =
            ContactNumbersService.numberInstance(contact);
          switch (contact.contact_type) {
            case CONTACT_TYPES.TOLL_FREE:
              groupedContacts.TOLL_FREE.push(formattedContact);
              break;
              1738215149;
            case CONTACT_TYPES.ALL_INDIA:
              groupedContacts.ALL_INDIA.push(formattedContact);
              break;
            case CONTACT_TYPES.INTERNATIONAL:
              groupedContacts.INTERNATIONAL.push(formattedContact);
              break;
          }
        }

        return {
          ...formattedCompany,
          categories: company.categories.map(
            CategoriesService.categoryInstance
          ),
          number: groupedContacts || null,
          companyUrls: company.company_urls.map(CompanyUrlsService.urlInstance),
        };
      });

      // Prepare the response following the established API pattern
      const response = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        companies: formattedCompanies,
      };

      res.handler.custom(STATUS_CODES.SUCCESS, API.COMPANIES_FETCHED, response);
    } catch (error) {
      console.log('Error fetching paginated companies:', error);

      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }

  async getCompanyById(req, res) {
    try {
      const { companyId } = req.params;

      const options = {
        include: [
          {
            association: 'categories',
            through: { attributes: [] },
          },
          {
            association: 'contact_numbers',
          },
        ],
      };

      // Find company with categories and contact numbers
      const company = await CompaniesService.findCompany(
        {
          id: companyId,
        },
        options
      );

      if (!company) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.NOT_FOUND, null);
      }

      const groupedContacts = {
        TOLL_FREE: [],
        ALL_INDIA: [],
        INTERNATIONAL: [],
      };

      for (const contact of company.contact_numbers) {
        const formattedContact = ContactNumbersService.numberInstance(contact);
        switch (contact.contact_type) {
          case CONTACT_TYPES.TOLL_FREE:
            groupedContacts.TOLL_FREE.push(formattedContact);
            break;
          case CONTACT_TYPES.ALL_INDIA:
            groupedContacts.ALL_INDIA.push(formattedContact);
            break;
          case CONTACT_TYPES.INTERNATIONAL:
            groupedContacts.INTERNATIONAL.push(formattedContact);
            break;
        }
      }

      const formattedCompany = {
        ...CompaniesService.companyInstance(company),
        categories:
          company.categories?.map(CategoriesService.categoryInstance) || [],
        contactNumbers: groupedContacts || [],
      };

      res.handler.custom(STATUS_CODES.SUCCESS, API.SUCCESS, formattedCompany);
    } catch (error) {
      console.log('Error fetching company details:', error);
      res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
    }
  }

  async getContactUpDown(req, res) {
    console.log('Starting getContactUpDown process');

    try {
      const companyId = req.body.companyId;
      if (!companyId) {
        return res.handler.custom(400, 'Company ID is required', null);
      }

      const company = await CompaniesService.findCompany(
        { id: companyId },
        { include: [{ association: 'contact_numbers' }], logging: false }
      );

      if (!company) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.NOT_FOUND, null);
      }

      const contacts = company.contact_numbers || [];
      if (contacts.length === 0) {
        return res.handler.success({
          company_id: companyId,
          contactsVotes: [],
        });
      }

      console.log(`Processing ${contacts.length} contact numbers`);

      // Start WebSocket logic
      const votesData = await new Promise((resolve) => {
        const websocket = new WebSocket('wss://updateadhaar.com:31040');
        const responseQueue = [];
        let responsesReceived = 0;
        let connectionClosed = false;

        const timeout = setTimeout(() => {
          console.log('Global timeout reached');
          finalize('Request timeout');
        }, 20000);

        function finalize(defaultError = null) {
          if (connectionClosed) return;
          connectionClosed = true;
          clearTimeout(timeout);

          try {
            if (websocket.readyState === WebSocket.OPEN) websocket.close();
          } catch (err) {
            console.error('Error closing WebSocket:', err);
          }

          const results = contacts.map((contact, i) => {
            const resp = responseQueue[i];
            if (resp && !resp.error) {
              return {
                contactId: contact.id,
                contactNumber: contact.contact_number,
                upVotes: Math.abs(resp.up) || 0,
                downVotes: Math.abs(resp.down) || 0,
              };
            } else {
              return {
                contactId: contact.id,
                contactNumber: contact.contact_number,
                upVotes: 0,
                downVotes: 0,
                error: defaultError || resp?.error || 'No response received',
              };
            }
          });

          resolve(results);
        }

        websocket.onopen = () => {
          console.log('WebSocket connected');
          setTimeout(() => {
            contacts.forEach((contact, idx) => {
              const data = {
                op: 'fetchupdown',
                website: 'indiacustomercare.com',
                nid: contact.id,
                phone: contact.contact_number,
                l: 'valid-license',
                src: 'iccapp',
                created: Date.now(),
                checksum: 40449494,
                source: 128,
                dataversion: 'NotEncrypted-v1.0',
                version: 'snd1.0',
                new_html_version: '1',
              };

              try {
                websocket.send(JSON.stringify(data));
                // console.log(`Sent request ${idx + 1}/${contacts.length}`);
              } catch (err) {
                console.error('WebSocket send error:', err);
              }
            });
          }, 100);
        };

        websocket.onmessage = (event) => {
          // console.log(`Response ${responsesReceived + 1}:`, event.data);
          try {
            const parsed = JSON.parse(event.data);
            responseQueue.push(parsed);
          } catch (err) {
            responseQueue.push({ up: 0, down: 0, error: 'Parse error' });
          }
          responsesReceived++;

          if (responseQueue.length >= contacts.length) {
            finalize();
          }
        };

        websocket.onerror = (err) => {
          console.error('WebSocket error:', err);
          finalize('Connection error');
        };

        websocket.onclose = (event) => {
          console.log(`WebSocket closed: ${event.code} - ${event.reason}`);
          finalize('Connection closed');
        };
      });

      const validVotes = votesData.filter((v) => !v.error);
      if (validVotes.length > 0) {
        await ContactNumbersService.bulkUpdateVotes(validVotes);
      }

      return res.handler.success({
        companyId: companyId,
        contactsVotes: votesData,
      });
    } catch (error) {
      console.error('Error in getContactUpDown:', error);
      return res.handler.custom(500, 'Server Error', error.message);
    }
  }
}

module.exports = CompanyController;
