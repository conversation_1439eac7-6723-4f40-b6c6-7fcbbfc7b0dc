const { STATUS_CODES } = require('../config/constants');
const { API } = require('../config/message');
const { validationResult } = require('express-validator');

const StaticService = new (require('../services/static.service'))();

class StaticController {
    async createStaticPage(req, res) {
        try {
            // Check for validation errors
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.handler.custom(
                    STATUS_CODES.BAD_REQUEST,
                    API.VALIDATION_ERROR,
                    errors.array()
                );
            }

            const data = req.body;
            const result = await StaticService.createStaticPage(data);

            res.handler.custom(
                STATUS_CODES.CREATED,
                API.STATIC_PAGE_CREATED,
                result
            );
        } catch (error) {
            console.error('Error creating static page:', error);
            res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
        }
    }

    async updateStaticPage(req, res) {
        try {
            // Check for validation errors
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.handler.custom(
                    STATUS_CODES.BAD_REQUEST,
                    API.VALIDATION_ERROR,
                    errors.array()
                );
            }

            const { id } = req.params;
            const data = req.body;

            const result = await StaticService.updateStaticPage(parseInt(id), data);

            if (!result) {
                return res.handler.custom(
                    STATUS_CODES.NOT_FOUND,
                    API.STATIC_PAGE_NOT_FOUND
                );
            }

            res.handler.custom(
                STATUS_CODES.SUCCESS,
                API.STATIC_PAGE_UPDATED,
                result
            );
        } catch (error) {
            console.error('Error updating static page:', error);
            res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
        }
    }

    async getStaticPages(_req, res) {
        try {
            const result = await StaticService.getStaticPages();

            if (!result || result.length === 0) {
                return res.handler.custom(
                    STATUS_CODES.NOT_FOUND,
                    API.NO_STATIC_PAGES_FOUND
                );
            }

            res.handler.custom(STATUS_CODES.SUCCESS, API.STATIC_PAGES_FETCHED, result);
        } catch (error) {
            console.error('Error fetching static pages:', error);
            res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
        }
    }

    async getStaticPageById(req, res) {
        try {
            const { id } = req.params;
            const result = await StaticService.getStaticPageById(parseInt(id));

            if (!result) {
                return res.handler.custom(
                    STATUS_CODES.NOT_FOUND,
                    API.STATIC_PAGE_NOT_FOUND
                );
            }

            res.handler.custom(STATUS_CODES.SUCCESS, API.SUCCESS, result);
        } catch (error) {
            console.error('Error fetching static page by ID:', error);
            res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
        }
    }

    async getStaticPageBySlug(req, res) {
        try {
            // Check for validation errors
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.handler.custom(
                    STATUS_CODES.BAD_REQUEST,
                    API.VALIDATION_ERROR,
                    errors.array()
                );
            }

            const { slug } = req.params;
            const result = await StaticService.getStaticPageBySlug(slug);

            if (!result) {
                return res.handler.custom(
                    STATUS_CODES.NOT_FOUND,
                    API.STATIC_PAGE_NOT_FOUND
                );
            }

            res.handler.custom(STATUS_CODES.SUCCESS, API.SUCCESS, result);
        } catch (error) {
            console.error('Error fetching static page by slug:', error);
            res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
        }
    }

    async deleteStaticPage(req, res) {
        try {
            // Check for validation errors
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.handler.custom(
                    STATUS_CODES.BAD_REQUEST,
                    API.VALIDATION_ERROR,
                    errors.array()
                );
            }

            const { id } = req.params;
            const result = await StaticService.deleteStaticPage(parseInt(id));

            if (!result) {
                return res.handler.custom(
                    STATUS_CODES.NOT_FOUND,
                    API.STATIC_PAGE_NOT_FOUND
                );
            }

            res.handler.custom(
                STATUS_CODES.SUCCESS,
                API.STATIC_PAGE_DELETED,
                { success: true }
            );
        } catch (error) {
            console.error('Error deleting static page:', error);
            res.handler.custom(STATUS_CODES.SERVER_ERROR, API.SERVER_ERROR, error);
        }
    }
}

module.exports = StaticController;
