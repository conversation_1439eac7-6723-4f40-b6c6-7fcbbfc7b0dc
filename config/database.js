require('dotenv').config();
const fs = require('fs');
const Sequelize = require('sequelize');
const config = require('../config/config');

const DB_CREDENTIAL = {
  ...config[process.env.NODE_ENV],

  logging: process.env.DB_LOGGING === 'true' ? console.log : false,

  // Reduce the pool size to stay within max_user_connections limit
  pool: {
    max: 5,     // Reduced from 25 to match max_user_connections
    min: 1,     // Reduced from 5
    acquire: 180000,
    idle: 60000,
    evict: 30000,
  },
  
  // Add retry logic for connection errors
  retry: {
    max: 3,
    match: [
      /SequelizeConnectionError/,
      /SequelizeConnectionRefusedError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/,
      /SequelizeInvalidConnectionError/,
      /SequelizeConnectionTimedOutError/,
      /TimeoutError/,
      /Operation timeout/,
      /ECONNRESET/,
      /ETIMEDOUT/,
      /EHOSTUNREACH/,
      /ENOTFOUND/,
      /ECONNREFUSED/,
      /ENETUNREACH/,
      /EAI_AGAIN/,
      /User.*has exceeded the.*max_user_connections/
    ],
    backoffBase: 1000,
    backoffExponent: 1.5,
    timeout: 60000,
    report: function(message) { 
      console.log(`Connection retry: ${message}`);
    }
  },
  
  dialectOptions: {
    connectTimeout: 60000,
    options: {
      requestTimeout: 300000 // 5 minutes
    },
    supportBigNumbers: true,
    bigNumberStrings: true,
    multipleStatements: true,
    flags: [
      '-FOUND_ROWS'
    ]
  }
};

const sequelize = new Sequelize(DB_CREDENTIAL);

sequelize
  .authenticate()
  .then(() => {
    console.log(
      `${DB_CREDENTIAL.database} database connected successfully :)\n`
    );
  })
  .catch((err) => {
    console.log('TCL: err', err);
    console.error('Unable to connect to the database :(\n');
  });

module.exports = {
  development: DB_CREDENTIAL,
  [process.env.NODE_ENV]: DB_CREDENTIAL,
  production: DB_CREDENTIAL,
  master: DB_CREDENTIAL,
  sequelize,
};
