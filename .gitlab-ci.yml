include:
  remote: 'https://${CI_SERVER_HOST}/public-resources/gitlab-ci/-/raw/master/templates/build.yaml'

stages:
  - build
  - deploy

variables:
  PROJECT: "india-customer-care-api"
  TECHNOLOGY: "nodejs"

build:
  stage: build
  extends: .build
  variables:
    BUILD_ARGS: "--build-arg APP_NAME=${PROJECT} --build-arg NODE_ENV=${CI_COMMIT_REF_NAME}"

deploy:
  stage: deploy
  extends: .deploy_devspace
  environment:
    url: https://$PROJECT.$DEV_BASE_DOMAIN
  variables:
    CONT_PORT: "5000" # container port
