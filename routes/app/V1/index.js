const express = require('express');
const router = express.Router();

const CategoryRoutes = require('./category.routes');
const CompanyRoutes = require('./company.routes');
const DataRoutes = require('./data.routes');
const FeedbackRoutes = require('./feedback.routes');
const StaticRoutes = require('./static.routes');

router.use('/category', CategoryRoutes);
router.use('/company', CompanyRoutes);
router.use('/data', DataRoutes);
router.use('/feedback', FeedbackRoutes);
router.use('/static', StaticRoutes);

module.exports = router;
