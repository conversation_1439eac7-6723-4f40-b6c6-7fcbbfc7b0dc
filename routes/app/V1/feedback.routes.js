const express = require('express');
const router = express.Router();

const { createFeedbackValidator } = require('../../../middlewares/validators/feedback.validator');
const { authentication } = require('../../../middlewares/auth.middleware');

const FeedbackController =
  new (require('../../../controllers/feedback.controller'))();

// Define routes
router
  .route('/')
  .post(
    createFeedbackValidator,
    authentication,
    FeedbackController.createFeedback
  );

module.exports = router;
