const express = require('express');
const router = express.Router();

const { getStaticPageBySlugValidator } = require('../../../middlewares/validators/static.validator');
const StaticController = new (require('../../../controllers/static.controller'))();

// Define routes
router.route('/').get(StaticController.getStaticPages);

router
  .route('/:slug')
  .get(getStaticPageBySlugValidator, StaticController.getStaticPageBySlug);

module.exports = router;
