const express = require('express');
const router = express.Router();

const {
  getPaginatedCompaniesValidator,
  getCompanyByIdValidator,
  getContactUpDownValidator,
} = require('../../../middlewares/validators/company.validator');
const { authentication } = require('../../../middlewares/auth.middleware');

const CompanyController =
  new (require('../../../controllers/company.controller'))();

// Define routes
router
  .route('/')
  .get(
    getPaginatedCompaniesValidator,
    authentication,
    CompanyController.getPaginatedCompaniesByCategory
  );

router
  .route('/get-all')
  .get(
    getPaginatedCompaniesValidator,
    authentication,
    CompanyController.getAllPaginatedCompanies
  );

router
  .route('/:companyId')
  .get(
    getCompanyByIdValidator,
    authentication,
    CompanyController.getCompanyById
  );

router
  .route('/up-down')
  .post(
    getContactUpDownValidator,
    authentication,
    CompanyController.getContactUpDown
  );

module.exports = router;
