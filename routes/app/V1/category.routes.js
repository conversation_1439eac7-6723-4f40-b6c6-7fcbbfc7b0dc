const express = require('express');
const router = express.Router();

const { authentication } = require('../../../middlewares/auth.middleware');
const {
  getAllCategoryValidator,
} = require('../../../middlewares/validators/category.validator');

const CategoryController =
  new (require('../../../controllers/category.controller'))();

// Define routes
router
  .route('/')
  .get(
    getAllCategoryValidator,
    authentication,
    CategoryController.getActiveCategories
  );

module.exports = router;
