const express = require("express");
const router = express.Router();
const { authentication } = require('../../../middlewares/auth.middleware');
const DataController = new (require('../../../controllers/data.controller'))();

// Define routes
router.route('/bulk-upload').post(DataController.bulkUploadData);

// Route to get all company-category relationships
router
  .route('/company-categories')
  .get(authentication, DataController.getAllCompanyCategories);

module.exports = router;
