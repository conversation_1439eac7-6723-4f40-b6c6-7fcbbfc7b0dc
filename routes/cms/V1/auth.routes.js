const express = require("express");
const router = express.Router();
const { authentication } = require('../../../middlewares/auth.middleware');
const {
  loginValidator,
  logoutValidator,
  forgotPasswordValidator,
  verifyOtpValidator,
  resetPasswordValidator,
} = require('../../../middlewares/validators/auth.validator');
const AuthController = new (require('../../../controllers/auth.controller'))();

// Define routes
router.post('/login', loginValidator, authentication, AuthController.login);

router.post(
  '/forgot-password',
  forgotPasswordValidator,
  authentication,
  AuthController.forgotPassword
);

router.post(
  '/verify-otp',
  verifyOtpValidator,
  authentication,
  AuthController.verifyOtp
);

router.post(
  '/reset-password',
  resetPasswordValidator,
  authentication,
  AuthController.resetPassword
);

router.get('/logout', logoutValidator, authentication, AuthController.logout);

module.exports = router;
