const express = require('express');
const router = express.Router();

const { authentication } = require('../../../middlewares/auth.middleware');
const {
  createStaticPageValidator,
  updateStaticPageValidator,
  getStaticPageBySlugValidator,
  deleteStaticPageValidator,
} = require('../../../middlewares/validators/static.validator');

const StaticController = new (require('../../../controllers/static.controller'))();

// Define routes
router
  .route('/')
  .get(authentication, StaticController.getStaticPages)
  .post(createStaticPageValidator, authentication, StaticController.createStaticPage);

router
  .route('/:id')
  .get(authentication, StaticController.getStaticPageById)
  .put(updateStaticPageValidator, authentication, StaticController.updateStaticPage)
  .delete(deleteStaticPageValidator, authentication, StaticController.deleteStaticPage);

router
  .route('/slug/:slug')
  .get(getStaticPageBySlugValidator, authentication, StaticController.getStaticPageBySlug);

module.exports = router;
