const express = require('express');
const router = express.Router();

const { authentication } = require('../../../middlewares/auth.middleware');
const { getPaginatedFeedbacksValidator } = require('../../../middlewares/validators/feedback.validator');

const FeedbackController = new (require('../../../controllers/feedback.controller'))();

// Define routes
router
  .route('/')
  .get(
    getPaginatedFeedbacksValidator,
    authentication,
    FeedbackController.getAllFeedbacks
  );

module.exports = router;
