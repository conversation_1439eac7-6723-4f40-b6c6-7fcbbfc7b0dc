const express = require('express');
const router = express.Router();

const { authentication } = require('../../../middlewares/auth.middleware');
const {
  getPaginatedCategoriesValidator,
  uploadCategoryIconValidator,
  updateCategoryValidator,
  togglePinCategoryValidator,
} = require('../../../middlewares/validators/category.validator');
const { Multer } = require('../../../middlewares/multer.middleware');
const {
  MAX_FILE_SIZE,
  ALLOWED_FILE_TYPES,
  ICON_FIELD_NAME,
} = require('../../../config/constants');

const CategoryController =
  new (require('../../../controllers/category.controller'))();

// Define routes
router
  .route('/')
  .get(
    getPaginatedCategoriesValidator,
    authentication,
    CategoryController.getPaginatedCategories
  )
  .put(
    updateCategoryValidator,
    authentication,
    CategoryController.updateCategory
  );

router
  .route('/pin')
  .post(
    togglePinCategoryValidator,
    authentication,
    CategoryController.togglePinCategory
  );

router
  .route('/icon-upload')
  .post(
    Multer(MAX_FILE_SIZE, ALLOWED_FILE_TYPES, ICON_FIELD_NAME),
    uploadCategoryIconValidator,
    authentication,
    CategoryController.uploadCategoryIcon
  );

module.exports = router;
