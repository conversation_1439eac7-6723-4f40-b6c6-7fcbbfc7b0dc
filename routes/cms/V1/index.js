const express = require('express');
const router = express.Router();

const AuthRoutes = require('./auth.routes');
const DataRoutes = require('./data.routes');
const CategoryRoutes = require('./category.routes');
const CompanyRoutes = require('./company.routes');
const StaticRoutes = require('./static.routes');
const FeedbackRoutes = require('./feedback.routes');

router.use('/auth', AuthRoutes);
router.use('/data', DataRoutes);
router.use('/category', CategoryRoutes);
router.use('/company', CompanyRoutes);
router.use('/static', StaticRoutes);
router.use('/feedback', FeedbackRoutes);

module.exports = router;
