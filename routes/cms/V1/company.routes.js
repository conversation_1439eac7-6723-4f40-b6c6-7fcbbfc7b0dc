const express = require('express');
const router = express.Router();

const { authentication } = require('../../../middlewares/auth.middleware');

const {
  getPaginatedCompaniesValidator,
  getCompanyByIdValidator,
} = require('../../../middlewares/validators/company.validator');

const CompanyController =
  new (require('../../../controllers/company.controller'))();

// Define routes
router
  .route('/')
  .get(
    getPaginatedCompaniesValidator,
    authentication,
    CompanyController.getAllPaginatedCompanies
  );

router
  .route('/:companyId')
  .get(
    getCompanyByIdValidator,
    authentication,
    CompanyController.getCompanyById
  );

module.exports = router;
