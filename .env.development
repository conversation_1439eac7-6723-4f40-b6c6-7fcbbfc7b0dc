NODE_ENV = development
PORT = 5000
APP_NAME = India Customer Care

WEBSITE_URL = "https://www.indiacustomercare.com"

BASE_URL = "https://india-customer-care-api.apps.openxcell.dev"

CMS_URL = "https://india-customer-care-cms.openxcell.dev"

DATA_UPDATE_URL = "https://updateadhaar.com/brsol/icc-app/page_nodes.json"

# Database configuration

DB_DATABASE = india_customer_care
DB_USERNAME = india_customer_care
DB_PASSWORD = "g6Da&3wy6)feG%!4"
DB_HOST = openxcell-development.c5uwiw99as4r.eu-west-1.rds.amazonaws.com
DB_PORT = 3306
DB_CONNECTION = mysql
DB_LOGGING = false

# Jwt configuration
APP_SECRET_KEY = 81ee2d73ebdc1cf72a4022c797830a0715456766c6845609f197b2301fbcb0b5
JWT_EXPIRATION = 1d
JWT_ALGORITHM = HS256

# Email Service configuration
EMAIL_SERVICE = gmail
EMAIL_ID = <EMAIL>
EMAIL_PASSWORD = 'vyej ltac fpfz yezd'
EMAIL_FROM = <EMAIL>